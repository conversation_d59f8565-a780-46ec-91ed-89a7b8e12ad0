const fs = require('fs')
const path = require('path')
const sass = require('sass')

/**
 * 构建独立的CSS文件
 * 将SCSS文件编译为CSS并输出到dist目录
 */
function buildCSS() {
  const inputFile = path.resolve(__dirname, 'src/style/iframe-external.scss')
  const outputFile = path.resolve(__dirname, 'dist/assets/livechat-iframe.css')
  
  try {
    // 编译SCSS
    const result = sass.compile(inputFile, {
      style: 'compressed', // 压缩输出
      sourceMap: false
    })
    
    // 确保输出目录存在
    const outputDir = path.dirname(outputFile)
    if (!fs.existsSync(outputDir)) {
      fs.mkdirSync(outputDir, { recursive: true })
    }
    
    // 写入CSS文件
    fs.writeFileSync(outputFile, result.css)
    
    console.log(`✅ CSS文件构建成功: ${outputFile}`)
    console.log(`📦 文件大小: ${(result.css.length / 1024).toFixed(2)} KB`)
    
    // 同时生成未压缩版本用于开发
    const devOutputFile = path.resolve(__dirname, 'dist/assets/livechat-iframe.dev.css')
    const devResult = sass.compile(inputFile, {
      style: 'expanded',
      sourceMap: true
    })
    
    fs.writeFileSync(devOutputFile, devResult.css)
    if (devResult.sourceMap) {
      fs.writeFileSync(devOutputFile + '.map', JSON.stringify(devResult.sourceMap))
    }
    
    console.log(`🔧 开发版本: ${devOutputFile}`)
    
  } catch (error) {
    console.error('❌ CSS构建失败:', error.message)
    process.exit(1)
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  buildCSS()
}

module.exports = { buildCSS }
